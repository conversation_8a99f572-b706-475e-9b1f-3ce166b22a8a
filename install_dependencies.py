#!/usr/bin/env python3
"""
Install Dependencies
Run this script to install all required dependencies for the project
"""

import subprocess
import sys
import os

def install_dependencies():
    """Install all project dependencies"""
    print("🚀 Installing SOAP Note App Dependencies...")
    print("=" * 50)
    
    try:
        # Install main app dependencies (already in venv)
        print("📦 Main app dependencies already installed in venv/")
        
        # Install backend dependencies
        print("\n📦 Installing backend dependencies...")
        backend_req = os.path.join("backend", "requirements.txt")
        if os.path.exists(backend_req):
            subprocess.run([sys.executable, "-m", "pip", "install", "-r", backend_req], check=True)
            print("✅ Backend dependencies installed!")
        
        # Install frontend dependencies
        print("\n📦 Installing frontend dependencies...")
        frontend_req = os.path.join("frontend", "requirements.txt")
        if os.path.exists(frontend_req):
            subprocess.run([sys.executable, "-m", "pip", "install", "-r", frontend_req], check=True)
            print("✅ Frontend dependencies installed!")
        
        print("\n🎉 All dependencies installed successfully!")
        print("\n🚀 You can now run:")
        print("   • Standalone: python fixed_voice_chat.py")
        print("   • Web Backend: python start_backend.py")
        print("   • Web Frontend: python start_frontend.py")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing dependencies: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    install_dependencies()
