# backend/main.py - Minimal FastAPI Backend
from fastapi import Fast<PERSON><PERSON>, File, UploadFile, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import sys
import os
import tempfile
import speech_recognition as sr
from groq import Groq

# Add parent directory to path to import fixed_voice_chat
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from fixed_voice_chat import FixedVoiceChat

app = FastAPI(title="SOAP Note Voice API", version="1.0.0")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize voice chat system (lazy loading)
voice_chat = None

def get_voice_chat():
    """Get voice chat instance with lazy loading"""
    global voice_chat
    if voice_chat is None:
        try:
            voice_chat = FixedVoiceChat()
        except Exception as e:
            print(f"Warning: Could not initialize voice chat: {e}")
            # Create a mock voice chat for API testing
            class MockVoiceChat:
                def generate_doctor_response(self, text):
                    return f"I understand you said: '{text}'. This is a mock response since voice chat couldn't initialize."
            voice_chat = MockVoiceChat()
    return voice_chat

# Store conversation
conversation_history = []

class ChatMessage(BaseModel):
    message: str

class ConversationItem(BaseModel):
    speaker: str
    text: str

@app.get("/")
async def root():
    return {"message": "SOAP Note Voice API is running!"}

@app.post("/transcribe")
async def transcribe_audio(audio: UploadFile = File(...)):
    """Transcribe uploaded audio to text"""
    try:
        # Save uploaded file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as temp_file:
            content = await audio.read()
            temp_file.write(content)
            temp_file_path = temp_file.name
        
        # Use speech recognition
        recognizer = sr.Recognizer()
        with sr.AudioFile(temp_file_path) as source:
            audio_data = recognizer.record(source)
            text = recognizer.recognize_google(audio_data, language='en-US')
        
        # Clean up temp file
        os.unlink(temp_file_path)
        
        return {"transcribed_text": text}
    
    except Exception as e:
        # Clean up temp file if it exists
        if 'temp_file_path' in locals():
            try:
                os.unlink(temp_file_path)
            except:
                pass
        raise HTTPException(status_code=400, detail=f"Transcription failed: {str(e)}")

@app.post("/chat")
async def chat_with_doctor(message: ChatMessage):
    """Get doctor response using the voice chat system"""
    try:
        # Use the voice chat system to generate response
        vc = get_voice_chat()
        doctor_response = vc.generate_doctor_response(message.message)
        
        # Add to conversation history
        conversation_history.append({"speaker": "Patient", "text": message.message})
        conversation_history.append({"speaker": "Doctor", "text": doctor_response})
        
        return {"response": doctor_response}
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Chat failed: {str(e)}")

@app.get("/conversation")
async def get_conversation():
    """Get current conversation history"""
    return {"conversation": conversation_history}

@app.post("/add_to_conversation")
async def add_to_conversation(item: ConversationItem):
    """Add item to conversation history"""
    conversation_history.append({"speaker": item.speaker, "text": item.text})
    return {"status": "added", "conversation": conversation_history}

@app.post("/clear_conversation")
async def clear_conversation():
    """Clear conversation history"""
    global conversation_history
    conversation_history = []
    return {"status": "cleared"}

@app.post("/generate_soap")
async def generate_soap_note():
    """Generate SOAP note from conversation"""
    if not conversation_history:
        raise HTTPException(status_code=400, detail="No conversation to analyze")
    
    # Format conversation
    formatted_conversation = "\n".join([f"{item['speaker']}: {item['text']}" for item in conversation_history])
    
    # Simple SOAP note template
    soap_note = f"""
**SOAP NOTE**

**S - Subjective:**
Patient reported symptoms and concerns during consultation.

**O - Objective:**
Clinical observations and examination findings.

**A - Assessment:**
Medical assessment based on patient consultation.

**P - Plan:**
Recommended treatment plan and follow-up.

**Conversation Log:**
{formatted_conversation}
"""
    
    return {"soap_note": soap_note}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
