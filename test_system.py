#!/usr/bin/env python3
"""
Test System Integration
Quick test to verify all components work together
"""

def test_imports():
    """Test if all imports work"""
    print("🧪 Testing imports...")
    
    try:
        # Test main app
        from fixed_voice_chat import FixedVoiceChat
        print("✅ fixed_voice_chat.py imports successfully")
    except Exception as e:
        print(f"❌ fixed_voice_chat.py import failed: {e}")
    
    try:
        # Test backend
        import sys
        sys.path.append('.')
        from backend.main import app
        print("✅ Backend imports successfully")
    except Exception as e:
        print(f"❌ Backend import failed: {e}")
    
    try:
        # Test frontend dependencies
        import streamlit
        import requests
        from audio_recorder_streamlit import audio_recorder
        print("✅ Frontend dependencies available")
    except Exception as e:
        print(f"❌ Frontend dependencies failed: {e}")

def test_api_endpoints():
    """Test backend API endpoints"""
    print("\n🧪 Testing API endpoints...")
    
    try:
        from backend.main import app
        from fastapi.testclient import TestClient
        
        client = TestClient(app)
        
        # Test root endpoint
        response = client.get("/")
        if response.status_code == 200:
            print("✅ Root endpoint works")
        else:
            print(f"❌ Root endpoint failed: {response.status_code}")
        
        # Test chat endpoint
        response = client.post("/chat", json={"message": "Hello doctor"})
        if response.status_code == 200:
            print("✅ Chat endpoint works")
            print(f"   Response: {response.json().get('response', 'No response')}")
        else:
            print(f"❌ Chat endpoint failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ API testing failed: {e}")

if __name__ == "__main__":
    print("🚀 SOAP Note App System Test")
    print("=" * 40)
    
    test_imports()
    test_api_endpoints()
    
    print("\n🎉 System test completed!")
    print("\n📋 Next steps:")
    print("1. Run: python start_backend.py (in one terminal)")
    print("2. Run: python start_frontend.py (in another terminal)")
    print("3. Open: http://localhost:8501 (frontend)")
    print("4. API docs: http://localhost:8000/docs (backend)")
