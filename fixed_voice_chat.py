import speech_recognition as sr
from gtts import gTTS
import pygame
import os
import time
from groq import Groq
import uuid

class FixedVoiceChat:
    def __init__(self):
        """Initialize fixed voice chat system"""
        print("🚀 Initializing Fixed Voice Chat...")
        
        self.recognizer = sr.Recognizer()
        self.microphone = sr.Microphone()

        self.groq_client = Groq(api_key="********************************************************")
        
        pygame.mixer.init()
  
        print("🎤 Testing microphone...")
        try:
            with self.microphone as source:
                self.recognizer.adjust_for_ambient_noise(source, duration=2)
            print("✅ Microphone ready!")
        except Exception as e:
            print(f"❌ Microphone error: {e}")
        
        print("✅ Voice Chat System Ready!")
    
    def listen_once(self):
        """Listen for one voice input with better error handling"""
        print("\n🎤 Listening... (Speak clearly)")
        
        try:
            with self.microphone as source:
                print("🔴 Recording... (Speak now)")
                
                audio = self.recognizer.listen(source, timeout=15, phrase_time_limit=8)
                
                print("🎧 Processing speech...")
                
                try:
                    text = self.recognizer.recognize_google(audio, language='en-US')
                    if text and text.strip() and text.lower() not in ['nothing', 'no', '']:
                        print(f"✅ You said: {text}")
                        return text
                    else:
                        print("🔄 No clear speech detected, try again...")
                        return ""
                except sr.UnknownValueError:
                    print("🔄 Could not understand audio, try speaking louder...")
                    return ""
                except sr.RequestError as e:
                    print(f"❌ Speech service error: {e}")
                    return ""
                    
        except sr.WaitTimeoutError:
            print("⏰ No speech detected in 15 seconds")
            return ""
        except Exception as e:
            print(f"❌ Error: {e}")
            return ""
    
    def generate_doctor_response(self, patient_text):
        """Generate doctor response using Groq"""
        try:
            system_prompt = """You are Dr. Smith, a compassionate and knowledgeable doctor. 
            Respond naturally to patient concerns with empathy and medical expertise. 
            Keep responses conversational, helpful, and under 40 words.
            Ask follow-up questions to understand symptoms better."""
            
            response = self.groq_client.chat.completions.create(
                model="llama3-8b-8192",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": f"Patient says: {patient_text}"}
                ],
                max_tokens=80,
                temperature=0.7
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            print(f"❌ Groq error: {e}")
            return self.get_fallback_response(patient_text)
    
    def get_fallback_response(self, patient_text):
        """Fallback medical responses"""
        text_lower = patient_text.lower()
        
        if "hello" in text_lower or "hi" in text_lower:
            return "Hello! I'm Dr. Smith. What brings you in today?"
        elif "headache" in text_lower:
            return "I understand you're having headaches. How long have they been bothering you?"
        elif "pain" in text_lower:
            return "Can you describe the pain? Where is it located?"
        elif "fever" in text_lower:
            return "A fever can indicate infection. Have you taken your temperature?"
        elif "cough" in text_lower:
            return "How long have you had this cough? Is it dry or productive?"
        elif "tired" in text_lower or "fatigue" in text_lower:
            return "Fatigue can have many causes. How long have you been feeling tired?"
        else:
            return "I understand. Can you tell me more about your symptoms?"
    
    def speak_response(self, text):
        """Make doctor speak with better file handling"""
        print(f"\n👨‍⚕️ Doctor: {text}")
        print("🔊 Doctor speaking...")
        
        try:
            audio_file = f"doctor_{uuid.uuid4().hex[:8]}.mp3"
            
            tts = gTTS(text=text, lang='en', slow=False)
            tts.save(audio_file)
            
            pygame.mixer.music.load(audio_file)
            pygame.mixer.music.play()
            
            while pygame.mixer.music.get_busy():
                time.sleep(0.1)
            
            time.sleep(0.5)
            try:
                if os.path.exists(audio_file):
                    os.remove(audio_file)
            except:
                pass 
                
        except Exception as e:
            print(f"❌ Speech error: {e}")
            print("📝 (Doctor response shown as text only)")
    
    def start_conversation(self):
        """Start the conversation"""
        print("\n🎯 Harry Bhai Style Voice Conversation!")
        print("=" * 50)
        print("💡 Tips:")
        print("   - Speak clearly and loudly")
        print("   - Wait for 'Recording...' message")
        print("   - Say 'bye' to end conversation")
        print("=" * 50)
        
        welcome = "Hello! I'm Dr. Smith. What's bothering you today?"
        self.speak_response(welcome)
        
        conversation_history = []
        
        try:
            while True:
                patient_text = self.listen_once()
                
                if not patient_text:
                    print("🔄 Try speaking again...")
                    continue
                
                conversation_history.append(f"Patient: {patient_text}")
                print(f"🤒 Patient: {patient_text}")
                
                if any(word in patient_text.lower() for word in ["bye", "goodbye", "exit", "quit", "stop"]):
                    goodbye = "Thank you for coming in. Take care and feel better soon!"
                    self.speak_response(goodbye)
                    break
                
                doctor_response = self.generate_doctor_response(patient_text)
                conversation_history.append(f"Doctor: {doctor_response}")
                
                self.speak_response(doctor_response)
                
                print("\n" + "-" * 40)
                
        except KeyboardInterrupt:
            print("\n🛑 Conversation ended by user")
        
        print("\n📋 Conversation Summary:")
        print("=" * 30)
        for msg in conversation_history:
            print(msg)
        
        print("\n✅ Conversation completed!")

if __name__ == "__main__":
    try:
        chat = FixedVoiceChat()
        chat.start_conversation()
    except Exception as e:
        print(f"❌ System error: {e}")
    
    print("\n🚀 Harry Bhai Style Voice Chat Complete!")
