#!/usr/bin/env python3
"""
Test Backend API
Quick test to verify backend is working
"""

import requests
import json

def test_backend():
    """Test backend endpoints"""
    print("🧪 Testing Backend API...")
    print("=" * 40)
    
    base_url = "http://localhost:8000"
    
    try:
        # Test root endpoint
        print("1. Testing root endpoint...")
        response = requests.get(f"{base_url}/")
        if response.status_code == 200:
            print(f"✅ Root endpoint: {response.json()}")
        else:
            print(f"❌ Root endpoint failed: {response.status_code}")
        
        # Test chat endpoint
        print("\n2. Testing chat endpoint...")
        chat_data = {"message": "Hello doctor, I have a headache"}
        response = requests.post(f"{base_url}/chat", json=chat_data)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Chat endpoint works!")
            print(f"   Patient: {chat_data['message']}")
            print(f"   Doctor: {result.get('response', 'No response')}")
        else:
            print(f"❌ Chat endpoint failed: {response.status_code}")
            print(f"   Error: {response.text}")
        
        # Test conversation endpoint
        print("\n3. Testing conversation endpoint...")
        response = requests.get(f"{base_url}/conversation")
        if response.status_code == 200:
            conversation = response.json().get('conversation', [])
            print(f"✅ Conversation endpoint works!")
            print(f"   Conversation items: {len(conversation)}")
        else:
            print(f"❌ Conversation endpoint failed: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to backend!")
        print("   Make sure backend is running on http://localhost:8000")
        print("   Run: python start_backend.py")
    except Exception as e:
        print(f"❌ Error testing backend: {e}")

if __name__ == "__main__":
    test_backend()
