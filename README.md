# 🎤 <PERSON> Style Voice SOAP Generator

**Real-time voice conversation system with AI doctor responses**

## 🚀 Quick Start Options

### Option 1: Standalone Voice Chat (Original)
```bash
python fixed_voice_chat.py
```

### Option 2: Web Interface (New!)
**Step 1: Start Backend**
```bash
python start_backend.py
```

**Step 2: Start Frontend (in new terminal)**
```bash
python start_frontend.py
```

**Step 3: Open Browser**
- Frontend: http://localhost:8501
- Backend API: http://localhost:8000/docs

## ✨ Features

- ✅ **Real-time voice recognition** (FREE - no API keys needed)
- ✅ **AI doctor responses** (Groq LLM with medical knowledge)
- ✅ **Voice synthesis** (Doctor speaks back)
- ✅ **Continuous conversation** (Like <PERSON> b<PERSON>'s video)
- ✅ **Web interface** (Streamlit frontend)
- ✅ **REST API** (FastAPI backend)
- ✅ **SOAP note generation** (Integrated)

## 📁 Project Structure

```
soap-note-app/
├── fixed_voice_chat.py     # 🎯 STANDALONE VOICE SYSTEM
├── start_backend.py        # 🚀 Backend starter
├── start_frontend.py       # 🚀 Frontend starter
├── backend/                # 🔧 FastAPI Backend
│   ├── main.py            # API server
│   └── requirements.txt   # Backend deps
├── frontend/              # 🖥️ Streamlit Frontend
│   ├── app.py            # Web interface
│   └── requirements.txt   # Frontend deps
└── venv/                  # 🐍 Python environment
```

## 🎯 How It Works

### Standalone Mode:
1. **Patient speaks** → Microphone captures voice
2. **Speech-to-text** → FREE Google recognition
3. **AI doctor responds** → Groq LLM generates response
4. **Text-to-speech** → Doctor speaks back
5. **Continuous loop** → Natural conversation flow

### Web Interface Mode:
1. **Frontend** → Streamlit web interface
2. **Backend** → FastAPI REST API
3. **Integration** → Uses fixed_voice_chat.py core logic
4. **Real-time** → Live conversation display
5. **SOAP notes** → Automatic generation

## 💡 Usage Tips

- Speak clearly and loudly
- Wait for "Recording..." message
- Say "bye" to end conversation
- Ensure microphone permissions are enabled
- For web interface, start backend first, then frontend

## 🔧 Requirements

- Python 3.8+
- Microphone access
- Internet connection (for Groq API)
- All dependencies in venv/

## 🎉 Ready to Use!

**Standalone:** `python fixed_voice_chat.py`
**Web Interface:** Run both `start_backend.py` and `start_frontend.py`
