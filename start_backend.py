#!/usr/bin/env python3
"""
Start Backend Server
Run this script to start the FastAPI backend server
"""

import subprocess
import sys
import os

def start_backend():
    """Start the FastAPI backend server"""
    print("🚀 Starting FastAPI Backend Server...")
    print("📍 Backend will run on: http://localhost:8000")
    print("📖 API docs will be available at: http://localhost:8000/docs")
    print("-" * 50)
    
    try:
        # Change to backend directory
        backend_dir = os.path.join(os.path.dirname(__file__), "backend")
        
        # Start uvicorn server
        subprocess.run([
            sys.executable, "-m", "uvicorn", 
            "main:app", 
            "--host", "0.0.0.0", 
            "--port", "8000", 
            "--reload"
        ], cwd=backend_dir)
        
    except KeyboardInterrupt:
        print("\n🛑 Backend server stopped")
    except Exception as e:
        print(f"❌ Error starting backend: {e}")

if __name__ == "__main__":
    start_backend()
